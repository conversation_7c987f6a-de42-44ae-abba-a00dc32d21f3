# SELA Web - Educational Platform

Nền tảng học tập trực tuyến hiện đại được xây dựng với React, TypeScript, Supabase và VPS.

## 🚀 Tech Stack

- **Frontend**: React 19 + TypeScript + Vite
- **Styling**: Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Realtime)
- **State Management**: Zustand + TanStack Query
- **Forms**: React Hook Form + Zod validation
- **UI Components**: Headless UI + Custom components
- **Deployment**: Netlify/Vercel (Frontend) + VPS (Custom API)

## 🏗️ Architecture

Dự án sử dụng **Feature-based Architecture** để dễ dàng maintain và scale:

```
src/
├── features/           # Feature modules
│   ├── auth/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── services/
│   │   └── types/
│   ├── courses/
│   └── progress/
├── shared/            # Reusable components
│   ├── components/
│   ├── hooks/
│   ├── utils/
│   └── types/
├── app/               # App-level configuration
│   ├── providers/
│   └── store/
├── lib/               # External libraries config
└── services/          # External service integration
```

## 🛠️ Setup Development

### Prerequisites

- Node.js 18+
- npm/yarn/bun
- Supabase account

### Installation

1. Clone repository:

```bash
git clone <repository-url>
cd SELA_Web
```

2. Install dependencies:

```bash
npm install
```

3. Setup environment variables:

```bash
cp env.example .env.local
```

Cập nhật `.env.local` với thông tin Supabase:

```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_API_URL=http://localhost:3001
```

4. Start development server:

```bash
npm run dev
```

## 📊 Database Schema

### Profiles Table

```sql
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  full_name TEXT,
  role TEXT DEFAULT 'student',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Courses Table

```sql
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10,2),
  instructor_id UUID REFERENCES profiles(id),
  thumbnail_url TEXT,
  video_url TEXT,
  duration INTEGER,
  level TEXT DEFAULT 'beginner',
  category TEXT,
  is_published BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### User Progress Table

```sql
CREATE TABLE user_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id),
  course_id UUID REFERENCES courses(id),
  progress_percentage INTEGER DEFAULT 0,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔐 Authentication

Project sử dụng Supabase Auth với:

- Email/Password authentication
- Social login (Google, GitHub) - coming soon
- Protected routes
- Role-based access control

## 🎨 UI Components

### Core Components

- **Button**: Với các variants (primary, secondary, outline, danger)
- **Input**: Với label, error handling, validation
- **Modal**: Responsive modal với overlay
- **Card**: Container component cho content

### Usage Example

```tsx
import { Button, Input, Modal } from "@/shared/components";

function MyComponent() {
  return (
    <div>
      <Input
        label="Email"
        type="email"
        placeholder="Enter your email"
        error={errors.email?.message}
      />
      <Button variant="primary" loading={loading}>
        Submit
      </Button>
    </div>
  );
}
```

## 📱 Features

### ✅ Implemented

- [x] Project setup với Vite + TypeScript
- [x] Tailwind CSS styling
- [x] Supabase integration
- [x] Authentication system
- [x] Core UI components
- [x] Feature-based architecture
- [x] Form validation với Zod

### 🚧 In Progress

- [ ] React Router setup
- [ ] Course management system
- [ ] Video player với progress tracking
- [ ] User dashboard

### 📋 Planned

- [ ] Payment integration
- [ ] Real-time features
- [ ] Mobile responsive design
- [ ] VPS API server
- [ ] Advanced video features

## 🚀 Deployment

### Frontend (Netlify/Vercel)

```bash
npm run build
# Deploy dist/ folder
```

### Backend (VPS)

```bash
# Setup VPS server
# Deploy custom API
# Configure nginx
```

## 📝 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🤝 Contributing

1. Fork repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -m 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Create Pull Request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.
