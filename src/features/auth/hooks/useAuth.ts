import { useState, useEffect } from 'react'
import { authService, type SignInData, type SignUpData } from '../services/authService'
import type { User } from '@/shared/types'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial user
    const getInitialUser = async () => {
      try {
        const currentUser = await authService.getCurrentUser()
        setUser(currentUser)
      } catch (error) {
        console.error('Error getting initial user:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialUser()

    // Listen for auth state changes
    const { data: { subscription } } = authService.onAuthStateChange((user) => {
      setUser(user)
      setLoading(false)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (data: SignInData) => {
    try {
      setLoading(true)
      await authService.signIn(data)
      // User state will be updated via onAuthStateChange
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const signUp = async (data: SignUpData) => {
    try {
      setLoading(true)
      await authService.signUp(data)
      // User state will be updated via onAuthStateChange
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const signOut = async () => {
    try {
      await authService.signOut()
      setUser(null)
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    }
  }

  const updateProfile = async (updates: Partial<User>) => {
    if (!user) {
      throw new Error('No authenticated user')
    }

    try {
      const updatedUser = await authService.updateProfile(updates)
      setUser(updatedUser)
      return updatedUser
    } catch (error) {
      console.error('Error updating profile:', error)
      throw error
    }
  }

  return {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    isAuthenticated: !!user,
  }
} 