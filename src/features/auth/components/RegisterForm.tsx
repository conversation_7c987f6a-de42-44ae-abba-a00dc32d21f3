import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button, Input } from '@/shared/components'
import { useAuthContext } from '@/app/providers'
import { useState } from 'react'

const registerSchema = z.object({
  fullName: z.string().min(2, 'Tên phải có ít nhất 2 ký tự'),
  email: z.string().email('Email không hợp lệ'),
  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Mật khẩu xác nhận không khớp',
  path: ['confirmPassword'],
})

type RegisterFormData = z.infer<typeof registerSchema>

interface RegisterFormProps {
  onClose: () => void
}

export function RegisterForm({ onClose }: RegisterFormProps) {
  const { signUp } = useAuthContext()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  })

  const onSubmit = async (data: RegisterFormData) => {
    try {
      setLoading(true)
      setError(null)
      await signUp({
        email: data.email,
        password: data.password,
        fullName: data.fullName,
      })
      onClose()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Đăng ký thất bại')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Input
          {...register('fullName')}
          type="text"
          label="Họ và tên"
          placeholder="Nhập họ và tên"
          error={errors.fullName?.message}
        />
      </div>
      
      <div>
        <Input
          {...register('email')}
          type="email"
          label="Email"
          placeholder="Nhập email của bạn"
          error={errors.email?.message}
        />
      </div>
      
      <div>
        <Input
          {...register('password')}
          type="password"
          label="Mật khẩu"
          placeholder="Nhập mật khẩu"
          error={errors.password?.message}
        />
      </div>
      
      <div>
        <Input
          {...register('confirmPassword')}
          type="password"
          label="Xác nhận mật khẩu"
          placeholder="Nhập lại mật khẩu"
          error={errors.confirmPassword?.message}
        />
      </div>

      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <div className="flex space-x-3">
        <Button
          type="submit"
          loading={loading}
          disabled={loading}
          className="flex-1"
        >
          Đăng ký
        </Button>
        <Button
          type="button"
          variant="secondary"
          onClick={onClose}
          disabled={loading}
        >
          Hủy
        </Button>
      </div>
    </form>
  )
} 