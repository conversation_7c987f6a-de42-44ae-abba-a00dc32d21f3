export interface User {
  id: string
  email: string
  full_name?: string
  role: 'student' | 'instructor' | 'admin'
  created_at: string
  updated_at: string
}

export interface Course {
  id: string
  title: string
  description: string
  price: number
  instructor_id: string
  instructor?: User
  thumbnail_url?: string
  video_url?: string
  duration?: number
  level: 'beginner' | 'intermediate' | 'advanced'
  category: string
  is_published: boolean
  created_at: string
  updated_at: string
}

export interface UserProgress {
  id: string
  user_id: string
  course_id: string
  progress_percentage: number
  completed_at?: string
  created_at: string
  updated_at: string
}

export interface Payment {
  id: string
  user_id: string
  course_id: string
  amount: number
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  payment_method: string
  created_at: string
  updated_at: string
}

export interface Enrollment {
  id: string
  user_id: string
  course_id: string
  enrolled_at: string
  completed_at?: string
}

export interface ApiResponse<T> {
  data: T
  error: null
}

export interface ApiError {
  data: null
  error: {
    message: string
    code?: string
  }
} 