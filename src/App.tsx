import { useState } from 'react'
import { AppProviders } from './app/providers'
import { useAuthContext } from './app/providers'
import { Modal, Button } from './shared/components'
import { LoginForm } from './features/auth/components/LoginForm'
import { RegisterForm } from './features/auth/components/RegisterForm'

function AppContent() {
  const { user, loading, signOut } = useAuthContext()
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [showRegisterModal, setShowRegisterModal] = useState(false)

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-600"><PERSON><PERSON> tải...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">SELA Web</h1>
            <div className="flex items-center space-x-4">
              {user ? (
                <div className="flex items-center space-x-4">
                  <span className="text-gray-700">Xin chào, {user.full_name || user.email}</span>
                  <Button variant="secondary" onClick={handleSignOut}>
                    Đăng xuất
                  </Button>
                </div>
              ) : (
                <div className="flex items-center space-x-4">
                  <Button variant="secondary" onClick={() => setShowLoginModal(true)}>
                    Đăng nhập
                  </Button>
                  <Button onClick={() => setShowRegisterModal(true)}>
                    Đăng ký
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Chào mừng đến với SELA Web
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Nền tảng học tập trực tuyến hiện đại
            </p>
            
            {user ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="card">
                  <h3 className="text-lg font-semibold mb-2">Khóa học của tôi</h3>
                  <p className="text-gray-600">Xem và tiếp tục học các khóa học đã đăng ký</p>
                </div>
                <div className="card">
                  <h3 className="text-lg font-semibold mb-2">Tiến độ học tập</h3>
                  <p className="text-gray-600">Theo dõi tiến độ và thành tích học tập</p>
                </div>
                <div className="card">
                  <h3 className="text-lg font-semibold mb-2">Khám phá</h3>
                  <p className="text-gray-600">Tìm kiếm và khám phá khóa học mới</p>
                </div>
              </div>
            ) : (
              <div className="card max-w-md mx-auto">
                <h3 className="text-lg font-semibold mb-2">Bắt đầu học ngay hôm nay</h3>
                <p className="text-gray-600 mb-4">
                  Đăng ký tài khoản để truy cập hàng ngàn khóa học chất lượng cao
                </p>
                <Button 
                  className="w-full" 
                  onClick={() => setShowRegisterModal(true)}
                >
                  Tạo tài khoản miễn phí
                </Button>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Auth Modals */}
      <Modal 
        isOpen={showLoginModal} 
        onClose={() => setShowLoginModal(false)}
        title="Đăng nhập"
      >
        <LoginForm onClose={() => setShowLoginModal(false)} />
      </Modal>

      <Modal 
        isOpen={showRegisterModal} 
        onClose={() => setShowRegisterModal(false)}
        title="Đăng ký tài khoản"
      >
        <RegisterForm onClose={() => setShowRegisterModal(false)} />
      </Modal>
    </div>
  )
}

function App() {
  return (
    <AppProviders>
      <AppContent />
    </AppProviders>
  )
}

export default App
