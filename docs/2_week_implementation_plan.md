# SELA_Web 2-Week Implementation Plan

**Timeline:** 14 days (2 weeks)  
**Tech Stack:** React + Supabase + VPS  
**Architecture:** Feature-based (Current structure 9/10)  
**Focus:** MVP với core educational features

---

## 📅 **Week 1: Foundation & Core Setup**

### **Day 1: Project Initialization & Environment**

#### Morning Tasks (4 hours)

- [ ] **Project Structure Optimization**

  ```bash
  # Merge helpers into utils (structure optimization)
  mv src/helpers/* src/utils/
  rmdir src/helpers

  # Create missing folders
  mkdir src/lib src/providers
  mkdir src/features/auth/{components,hooks,services,types}
  mkdir src/features/courses/{components,hooks,services,types}
  mkdir src/features/progress/{components,hooks,services,types}
  ```

- [ ] **Dependencies Installation**

  ```bash
  # Core dependencies
  npm install @supabase/supabase-js
  npm install react-router-dom zustand @tanstack/react-query
  npm install react-hook-form @hookform/resolvers zod

  # UI dependencies
  npm install @headlessui/react @heroicons/react
  npm install date-fns react-player

  # Development dependencies
  npm install -D @types/react @types/react-dom
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Environment Configuration**

  ```typescript
  // .env.local setup
  VITE_SUPABASE_URL=your_supabase_url
  VITE_SUPABASE_ANON_KEY=your_supabase_key
  VITE_API_URL=http://localhost:3001
  ```

- [ ] **Supabase Project Setup**

  - Create Supabase project
  - Configure authentication providers
  - Set up basic database schema

- [ ] **Vite Configuration Optimization**

  ```typescript
  // vite.config.ts optimization
  import { defineConfig } from "vite";
  import react from "@vitejs/plugin-react";
  import path from "path";

  export default defineConfig({
    plugins: [react()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    server: {
      port: 3000,
      hmr: { overlay: false },
    },
  });
  ```

#### Evening (2 hours)

- [ ] **Git Setup & Documentation**
  - Initialize git repository
  - Create .gitignore
  - Document environment setup
  - Create basic README

---

### **Day 2: Core Infrastructure**

#### Morning Tasks (4 hours)

- [ ] **Supabase Client Configuration**

  ```typescript
  // src/lib/supabase.ts
  import { createClient } from "@supabase/supabase-js";

  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL!;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY!;

  export const supabase = createClient(supabaseUrl, supabaseAnonKey);
  ```

- [ ] **TanStack Query Setup**

  ```typescript
  // src/lib/queryClient.ts
  import { QueryClient } from "@tanstack/react-query";

  export const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        cacheTime: 10 * 60 * 1000, // 10 minutes
      },
    },
  });
  ```

- [ ] **Global Types Definition**

  ```typescript
  // src/types/index.ts
  export interface User {
    id: string;
    email: string;
    full_name?: string;
    role: "student" | "instructor" | "admin";
    created_at: string;
  }

  export interface Course {
    id: string;
    title: string;
    description: string;
    price: number;
    instructor_id: string;
    created_at: string;
  }
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Database Schema Creation**

  ```sql
  -- Supabase SQL Editor
  -- Users table (extends auth.users)
  CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    full_name TEXT,
    role TEXT DEFAULT 'student',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Courses table
  CREATE TABLE courses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    instructor_id UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- User progress table
  CREATE TABLE user_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id),
    course_id UUID REFERENCES courses(id),
    progress_percentage INTEGER DEFAULT 0,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  ```

- [ ] **Row Level Security Setup**

  ```sql
  -- Enable RLS
  ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
  ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
  ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;

  -- Create policies
  CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

  CREATE POLICY "Courses are viewable by everyone" ON courses
    FOR SELECT USING (true);
  ```

#### Evening (2 hours)

- [ ] **Provider Setup**
  ```typescript
  // src/providers/AuthProvider.tsx
  // src/providers/QueryProvider.tsx
  // src/providers/index.tsx
  ```

---

### **Day 3: Authentication System**

#### Morning Tasks (4 hours)

- [ ] **Auth Service Implementation**

  ```typescript
  // src/features/auth/services/authService.ts
  import { supabase } from "@/lib/supabase";

  export const authService = {
    async signUp(email: string, password: string, fullName: string) {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: { full_name: fullName },
        },
      });
      return { data, error };
    },

    async signIn(email: string, password: string) {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { data, error };
    },

    async signOut() {
      const { error } = await supabase.auth.signOut();
      return { error };
    },
  };
  ```

- [ ] **Auth Hooks**

  ```typescript
  // src/features/auth/hooks/useAuth.ts
  import { useState, useEffect } from "react";
  import { supabase } from "@/lib/supabase";

  export function useAuth() {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      // Implementation
    }, []);

    return { user, loading, signIn, signUp, signOut };
  }
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Auth Components**

  ```typescript
  // src/features/auth/components/LoginForm.tsx
  // src/features/auth/components/RegisterForm.tsx
  // src/features/auth/components/AuthGuard.tsx
  ```

- [ ] **Auth Pages**
  ```typescript
  // src/pages/LoginPage.tsx
  // src/pages/RegisterPage.tsx
  ```

#### Evening (2 hours)

- [ ] **Auth Testing**
  - Test login/register flow
  - Verify database connections
  - Test auth state persistence

---

### **Day 4: Routing & Navigation**

#### Morning Tasks (4 hours)

- [ ] **React Router Setup**

  ```typescript
  // src/App.tsx
  import { BrowserRouter, Routes, Route } from "react-router-dom";
  import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

  function App() {
    return (
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <Routes>{/* Route definitions */}</Routes>
        </BrowserRouter>
      </QueryClientProvider>
    );
  }
  ```

- [ ] **Layout Components**
  ```typescript
  // src/layouts/AppLayout.tsx
  // src/layouts/AuthLayout.tsx
  // src/components/Navigation.tsx
  // src/components/Header.tsx
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Navigation Implementation**

  ```typescript
  // src/components/Navigation.tsx - Responsive navigation
  // src/components/UserMenu.tsx - User dropdown
  // src/components/MobileMenu.tsx - Mobile navigation
  ```

- [ ] **Protected Routes**

  ```typescript
  // src/components/ProtectedRoute.tsx
  import { useAuth } from "@/features/auth/hooks/useAuth";
  import { Navigate } from "react-router-dom";

  export function ProtectedRoute({ children }) {
    const { user, loading } = useAuth();

    if (loading) return <div>Loading...</div>;
    if (!user) return <Navigate to="/login" />;

    return children;
  }
  ```

#### Evening (2 hours)

- [ ] **Responsive Design Testing**
  - Test mobile navigation
  - Verify route protection
  - Test user experience flow

---

### **Day 5: Course Management Foundation**

#### Morning Tasks (4 hours)

- [ ] **Course Service Layer**

  ```typescript
  // src/features/courses/services/courseService.ts
  import { supabase } from "@/lib/supabase";
  import type { Course } from "@/types";

  export const courseService = {
    async getAll(): Promise<Course[]> {
      const { data, error } = await supabase
        .from("courses")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    },

    async getById(id: string): Promise<Course> {
      const { data, error } = await supabase
        .from("courses")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },

    async create(course: Omit<Course, "id" | "created_at">): Promise<Course> {
      const { data, error } = await supabase
        .from("courses")
        .insert([course])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
  };
  ```

- [ ] **Course Hooks**

  ```typescript
  // src/features/courses/hooks/useCourses.ts
  import { useQuery } from "@tanstack/react-query";
  import { courseService } from "../services/courseService";

  export function useCourses() {
    return useQuery({
      queryKey: ["courses"],
      queryFn: courseService.getAll,
    });
  }

  export function useCourse(id: string) {
    return useQuery({
      queryKey: ["courses", id],
      queryFn: () => courseService.getById(id),
      enabled: !!id,
    });
  }
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Course Components**

  ```typescript
  // src/features/courses/components/CourseCard.tsx
  // src/features/courses/components/CourseList.tsx
  // src/features/courses/components/CourseDetail.tsx
  ```

- [ ] **Course Pages**
  ```typescript
  // src/pages/CoursesPage.tsx
  // src/pages/CourseDetailPage.tsx
  ```

#### Evening (2 hours)

- [ ] **State Management Setup**

  ```typescript
  // src/store/courseStore.ts using Zustand
  import { create } from "zustand";

  interface CourseStore {
    selectedCourse: Course | null;
    setSelectedCourse: (course: Course | null) => void;
  }

  export const useCourseStore = create<CourseStore>((set) => ({
    selectedCourse: null,
    setSelectedCourse: (course) => set({ selectedCourse: course }),
  }));
  ```

---

### **Day 6: UI Components & Styling**

#### Morning Tasks (4 hours)

- [ ] **Tailwind CSS Configuration**

  ```typescript
  // tailwind.config.js optimization for educational platform
  module.exports = {
    content: ["./src/**/*.{js,ts,jsx,tsx}"],
    theme: {
      extend: {
        colors: {
          primary: {
            50: "#eff6ff",
            500: "#3b82f6",
            900: "#1e3a8a",
          },
        },
      },
    },
  };
  ```

- [ ] **Shared Components**
  ```typescript
  // src/shared/components/Button.tsx
  // src/shared/components/Input.tsx
  // src/shared/components/Modal.tsx
  // src/shared/components/LoadingSpinner.tsx
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Educational-Specific Components**

  ```typescript
  // src/shared/components/ProgressBar.tsx
  // src/shared/components/VideoPlayer.tsx
  // src/shared/components/CourseCard.tsx
  // src/shared/components/LessonItem.tsx
  ```

- [ ] **Form Components**
  ```typescript
  // src/shared/components/forms/FormInput.tsx
  // src/shared/components/forms/FormTextarea.tsx
  // src/shared/components/forms/FormSelect.tsx
  ```

#### Evening (2 hours)

- [ ] **Component Testing & Storybook Setup (Optional)**
  - Test component renders
  - Verify responsive design
  - Create component documentation

---

### **Day 7: Week 1 Integration & Testing**

#### Morning Tasks (4 hours)

- [ ] **Feature Integration**

  ```typescript
  // src/features/auth/index.ts - Export all auth features
  // src/features/courses/index.ts - Export all course features
  // Connect all pieces together
  ```

- [ ] **Error Handling**
  ```typescript
  // src/shared/components/ErrorBoundary.tsx
  // src/shared/components/ErrorMessage.tsx
  // src/shared/hooks/useErrorHandler.ts
  ```

#### Afternoon Tasks (4 hours)

- [ ] **End-to-End Testing**

  - User registration flow
  - Login/logout functionality
  - Course browsing
  - Navigation testing

- [ ] **Performance Optimization**
  ```typescript
  // Code splitting setup
  // Lazy loading components
  // Bundle size optimization
  ```

#### Evening (2 hours)

- [ ] **Week 1 Review & Documentation**
  - Code review
  - Update documentation
  - Prepare for Week 2

---

## 📅 **Week 2: Features & Integration**

### **Day 8: Video Player & Progress Tracking**

#### Morning Tasks (4 hours)

- [ ] **Video Player Integration**

  ```typescript
  // src/features/courses/components/VideoPlayer.tsx
  import ReactPlayer from "react-player";
  import { useAuth } from "@/features/auth/hooks/useAuth";

  export function VideoPlayer({ videoUrl, courseId, onProgress }) {
    const { user } = useAuth();

    const handleProgress = useCallback(
      (progress) => {
        if (user) {
          // Update progress in Supabase
          supabase.from("user_progress").upsert({
            user_id: user.id,
            course_id: courseId,
            progress_percentage: progress * 100,
          });
        }
        onProgress?.(progress);
      },
      [user, courseId, onProgress]
    );

    return (
      <ReactPlayer
        url={videoUrl}
        controls
        onProgress={handleProgress}
        config={{
          file: { attributes: { preload: "metadata" } },
        }}
      />
    );
  }
  ```

- [ ] **Progress Service**

  ```typescript
  // src/features/progress/services/progressService.ts
  export const progressService = {
    async getUserProgress(userId: string, courseId: string) {
      const { data, error } = await supabase
        .from("user_progress")
        .select("*")
        .eq("user_id", userId)
        .eq("course_id", courseId)
        .single();

      return { data, error };
    },

    async updateProgress(userId: string, courseId: string, percentage: number) {
      const { error } = await supabase.from("user_progress").upsert({
        user_id: userId,
        course_id: courseId,
        progress_percentage: percentage,
      });

      return { error };
    },
  };
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Real-time Progress Tracking**

  ```typescript
  // src/features/progress/hooks/useProgress.ts
  import { useState, useEffect } from "react";
  import { supabase } from "@/lib/supabase";

  export function useProgress(userId: string, courseId: string) {
    const [progress, setProgress] = useState(0);

    useEffect(() => {
      // Subscribe to real-time changes
      const subscription = supabase
        .channel(`progress-${userId}-${courseId}`)
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "user_progress",
            filter: `user_id=eq.${userId}&course_id=eq.${courseId}`,
          },
          (payload) => {
            if (payload.new) {
              setProgress(payload.new.progress_percentage);
            }
          }
        )
        .subscribe();

      return () => subscription.unsubscribe();
    }, [userId, courseId]);

    return { progress, setProgress };
  }
  ```

- [ ] **Progress Components**
  ```typescript
  // src/features/progress/components/ProgressBar.tsx
  // src/features/progress/components/ProgressTracker.tsx
  // src/features/progress/components/ProgressDashboard.tsx
  ```

#### Evening (2 hours)

- [ ] **Video Player Testing**
  - Test video playback
  - Verify progress tracking
  - Test real-time updates

---

### **Day 9: VPS Server Setup**

#### Morning Tasks (4 hours)

- [ ] **VPS Server Configuration**

  ```bash
  # VPS setup script
  #!/bin/bash
  # Update system
  apt update && apt upgrade -y

  # Install Node.js 20
  curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
  apt-get install -y nodejs

  # Install Redis
  apt-get install -y redis-server
  systemctl enable redis-server

  # Install PM2
  npm install -g pm2

  # Setup firewall
  ufw allow 22 && ufw allow 80 && ufw allow 443 && ufw allow 3001
  ufw enable
  ```

- [ ] **API Server Setup**

  ```typescript
  // server/src/app.ts
  import express from "express";
  import cors from "cors";
  import helmet from "helmet";

  const app = express();

  app.use(helmet());
  app.use(
    cors({
      origin: process.env.FRONTEND_URL || "http://localhost:3000",
      credentials: true,
    })
  );

  app.use(express.json({ limit: "10mb" }));

  // Routes
  app.use("/api/bmad", bmadRoutes);
  app.use("/api/video", videoRoutes);

  app.get("/health", (req, res) => {
    res.json({ status: "ok", timestamp: new Date().toISOString() });
  });

  export default app;
  ```

#### Afternoon Tasks (4 hours)

- [ ] **BMad Integration API**

  ```typescript
  // server/src/routes/bmad.ts
  import express from "express";
  import { bmadProcessor } from "../services/bmadProcessor";

  const router = express.Router();

  router.post("/process", async (req, res) => {
    try {
      const { taskType, data } = req.body;
      const result = await bmadProcessor.execute(taskType, data);
      res.json({ success: true, result });
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  });

  export default router;
  ```

- [ ] **File Processing Setup**

  ```typescript
  // server/src/services/fileProcessor.ts
  import ffmpeg from "fluent-ffmpeg";

  export class FileProcessor {
    static async processVideo(inputPath: string, outputPath: string) {
      return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
          .output(outputPath)
          .videoCodec("libx264")
          .audioCodec("aac")
          .on("end", resolve)
          .on("error", reject)
          .run();
      });
    }
  }
  ```

#### Evening (2 hours)

- [ ] **Deployment Setup**
  ```javascript
  // ecosystem.config.js
  module.exports = {
    apps: [
      {
        name: "sela-api",
        script: "./dist/server.js",
        instances: 2,
        exec_mode: "cluster",
        env: {
          NODE_ENV: "production",
          PORT: 3001,
        },
      },
    ],
  };
  ```

---

### **Day 10: File Upload & Storage**

#### Morning Tasks (4 hours)

- [ ] **Supabase Storage Setup**

  ```sql
  -- Create storage bucket
  INSERT INTO storage.buckets (id, name, public)
  VALUES ('course-materials', 'course-materials', true);

  -- Storage policies
  CREATE POLICY "Allow authenticated uploads" ON storage.objects
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');
  ```

- [ ] **File Upload Service**

  ```typescript
  // src/services/storageService.ts
  import { supabase } from "@/lib/supabase";

  export const storageService = {
    async uploadFile(file: File, path: string) {
      const { data, error } = await supabase.storage
        .from("course-materials")
        .upload(path, file);

      if (error) throw error;
      return data;
    },

    async getFileUrl(path: string) {
      const { data } = supabase.storage
        .from("course-materials")
        .getPublicUrl(path);

      return data.publicUrl;
    },
  };
  ```

#### Afternoon Tasks (4 hours)

- [ ] **File Upload Components**

  ```typescript
  // src/shared/components/FileUpload.tsx
  // src/shared/components/ImageUpload.tsx
  // src/shared/components/VideoUpload.tsx
  ```

- [ ] **Course Creation Form**

  ```typescript
  // src/features/courses/components/CreateCourseForm.tsx
  import { useForm } from "react-hook-form";
  import { zodResolver } from "@hookform/resolvers/zod";
  import { z } from "zod";

  const courseSchema = z.object({
    title: z.string().min(1),
    description: z.string().min(10),
    price: z.number().min(0),
  });

  export function CreateCourseForm() {
    const {
      register,
      handleSubmit,
      formState: { errors },
    } = useForm({
      resolver: zodResolver(courseSchema),
    });

    const onSubmit = async (data) => {
      // Handle form submission
    };

    return <form onSubmit={handleSubmit(onSubmit)}>{/* Form fields */}</form>;
  }
  ```

#### Evening (2 hours)

- [ ] **File Upload Testing**
  - Test image uploads
  - Test video uploads
  - Verify file permissions

---

### **Day 11: User Dashboard & Analytics**

#### Morning Tasks (4 hours)

- [ ] **Dashboard Components**

  ```typescript
  // src/features/dashboard/components/StudentDashboard.tsx
  // src/features/dashboard/components/InstructorDashboard.tsx
  // src/features/dashboard/components/ProgressOverview.tsx
  ```

- [ ] **Analytics Service**

  ```typescript
  // src/services/analyticsService.ts
  export const analyticsService = {
    async getUserStats(userId: string) {
      const { data, error } = await supabase
        .from("user_progress")
        .select(
          `
          course_id,
          progress_percentage,
          courses (title, description)
        `
        )
        .eq("user_id", userId);

      return { data, error };
    },
  };
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Dashboard Pages**

  ```typescript
  // src/pages/DashboardPage.tsx
  // src/pages/StudentProfilePage.tsx
  // src/pages/InstructorProfilePage.tsx
  ```

- [ ] **Chart Components**
  ```typescript
  // src/shared/components/charts/ProgressChart.tsx
  // src/shared/components/charts/CourseStatsChart.tsx
  ```

#### Evening (2 hours)

- [ ] **Dashboard Testing**
  - Test user statistics
  - Verify progress charts
  - Test responsive design

---

### **Day 12: Payment Integration (Basic)**

#### Morning Tasks (4 hours)

- [ ] **Payment Schema**

  ```sql
  -- Create payments table
  CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id),
    course_id UUID REFERENCES courses(id),
    amount DECIMAL(10,2),
    status TEXT DEFAULT 'pending',
    payment_method TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  ```

- [ ] **Payment Service**

  ```typescript
  // src/features/payments/services/paymentService.ts
  export const paymentService = {
    async createPayment(userId: string, courseId: string, amount: number) {
      const { data, error } = await supabase
        .from("payments")
        .insert([
          {
            user_id: userId,
            course_id: courseId,
            amount: amount,
            status: "pending",
          },
        ])
        .select()
        .single();

      return { data, error };
    },
  };
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Payment Components**

  ```typescript
  // src/features/payments/components/PaymentForm.tsx
  // src/features/payments/components/PaymentHistory.tsx
  // src/features/payments/components/CourseEnrollment.tsx
  ```

- [ ] **Course Enrollment Logic**

  ```typescript
  // src/features/courses/hooks/useEnrollment.ts
  export function useEnrollment() {
    const enrollInCourse = async (courseId: string) => {
      // Payment processing logic
      // Update user_progress table
      // Send confirmation
    };

    return { enrollInCourse };
  }
  ```

#### Evening (2 hours)

- [ ] **Payment Testing**
  - Test enrollment flow
  - Verify payment records
  - Test course access

---

### **Day 13: Testing & Bug Fixes**

#### Morning Tasks (4 hours)

- [ ] **End-to-End Testing**

  ```typescript
  // src/tests/e2e/userFlow.test.ts
  // Test complete user journey:
  // 1. Registration
  // 2. Course browsing
  // 3. Course enrollment
  // 4. Video watching
  // 5. Progress tracking
  ```

- [ ] **Unit Testing**
  ```typescript
  // src/features/auth/services/__tests__/authService.test.ts
  // src/features/courses/hooks/__tests__/useCourses.test.ts
  // src/shared/components/__tests__/Button.test.tsx
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Bug Fixes & Optimizations**

  - Fix authentication edge cases
  - Optimize video loading
  - Fix responsive design issues
  - Improve error handling

- [ ] **Performance Testing**
  ```bash
  # Bundle analysis
  npm run build
  npm install -g webpack-bundle-analyzer
  npx webpack-bundle-analyzer dist/static/js/*.js
  ```

#### Evening (2 hours)

- [ ] **Code Review & Refactoring**
  - Clean up unused code
  - Improve code documentation
  - Optimize imports

---

### **Day 14: Deployment & Documentation**

#### Morning Tasks (4 hours)

- [ ] **Production Build Setup**

  ```typescript
  // vite.config.ts production optimization
  export default defineConfig({
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["react", "react-dom"],
            supabase: ["@supabase/supabase-js"],
            ui: ["@headlessui/react"],
          },
        },
      },
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
    },
  });
  ```

- [ ] **Environment Configuration**
  ```bash
  # Production environment setup
  VITE_SUPABASE_URL=production_url
  VITE_SUPABASE_ANON_KEY=production_key
  VITE_API_URL=https://api.sela-web.com
  ```

#### Afternoon Tasks (4 hours)

- [ ] **Deployment**

  ```yaml
  # netlify.toml
  [build]
    command = "npm run build"
    publish = "dist"

  [[redirects]]
    from = "/*"
    to = "/index.html"
    status = 200
  ```

- [ ] **VPS Deployment**
  ```bash
  # Deploy API server to VPS
  git clone repo
  npm install
  npm run build
  pm2 start ecosystem.config.js
  ```

#### Evening (2 hours)

- [ ] **Documentation & Handoff**

  ```markdown
  # Project Documentation

  - Setup instructions
  - Development workflow
  - Deployment process
  - API documentation
  - Component library
  ```

---

## 🎯 **2-Week Deliverables**

### **Core Features Completed**

- [ ] ✅ User authentication (login/register)
- [ ] ✅ Course browsing and management
- [ ] ✅ Video player with progress tracking
- [ ] ✅ Real-time progress updates
- [ ] ✅ User dashboard and analytics
- [ ] ✅ File upload and storage
- [ ] ✅ Basic payment integration
- [ ] ✅ Responsive design
- [ ] ✅ VPS server setup
- [ ] ✅ Production deployment

### **Technical Architecture**

- [ ] ✅ Feature-based folder structure
- [ ] ✅ React + TypeScript + Vite
- [ ] ✅ Supabase integration
- [ ] ✅ TanStack Query for data fetching
- [ ] ✅ Zustand for state management
- [ ] ✅ Tailwind CSS for styling
- [ ] ✅ React Hook Form + Zod validation
- [ ] ✅ Error handling and loading states

### **Performance & Quality**

- [ ] ✅ Bundle size optimization
- [ ] ✅ Code splitting and lazy loading
- [ ] ✅ Responsive design (mobile-first)
- [ ] ✅ Error boundaries and error handling
- [ ] ✅ Loading states and skeletons
- [ ] ✅ SEO optimization (meta tags)

## 📊 **Success Metrics**

| Metric                 | Target           | Expected Result      |
| ---------------------- | ---------------- | -------------------- |
| **Build Time**         | < 10 seconds     | ✅ Vite fast builds  |
| **Bundle Size**        | < 500KB          | ✅ Code splitting    |
| **First Load**         | < 2 seconds      | ✅ Optimized assets  |
| **Mobile Performance** | 90+ Lighthouse   | ✅ Responsive design |
| **User Registration**  | Working flow     | ✅ Supabase auth     |
| **Video Playback**     | Smooth streaming | ✅ React Player      |
| **Real-time Updates**  | < 1 second delay | ✅ Supabase realtime |

## 🚨 **Risk Mitigation**

### **Week 1 Risks**

- **Supabase setup delays** → Have backup local PostgreSQL
- **Authentication issues** → Use Supabase built-in auth
- **Component complexity** → Start with simple components

### **Week 2 Risks**

- **VPS deployment issues** → Have local development fallback
- **Video streaming problems** → Use simple file uploads first
- **Payment integration delays** → Implement basic enrollment without payment

## 🏁 **Final Notes**

**This 2-week plan delivers a fully functional MVP** với:

- Complete user authentication
- Course management system
- Video streaming with progress tracking
- Real-time features
- Production-ready deployment

**Post-2-week priorities:**

1. Advanced BMad integration
2. Enhanced payment processing (VNPay/MoMo)
3. Advanced analytics and reporting
4. Mobile app development
5. Advanced video features (transcoding, subtitles)

**Total estimated effort:** 110-120 hours over 14 days
**Team requirement:** 1-2 full-stack developers
**Budget:** $21-36/month operational costs
