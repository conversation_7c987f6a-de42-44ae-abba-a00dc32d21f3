# SELA_Web Migration Plan: Next.js → React + Supabase

## 🎯 **Migration Overview**

**From:** Next.js 14 + PostgreSQL + Redis + Vercel Stack  
**To:** React 18 + Supabase + Netlify Stack  
**Timeline:** 6-8 weeks  
**Risk Level:** Medium

## 📋 **Phase 1: Foundation Setup (Week 1-2)**

### Step 1: React Project Setup

```bash
# Create new React project
npm create vite@latest sela-web-react -- --template react-ts
cd sela-web-react

# Install core dependencies
npm install @supabase/supabase-js
npm install react-router-dom zustand @tanstack/react-query
npm install tailwindcss @headlessui/react
npm install video.js react-player
```

### Step 2: Supabase Setup

```sql
-- Migrate existing PostgreSQL schema to Supabase
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title JSONB NOT NULL,
  content JSONB,
  price DECIMAL(10,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  course_id UUID REFERENCES courses(id),
  progress_percentage INTEGER DEFAULT 0,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Courses are viewable by everyone"
  ON courses FOR SELECT
  USING (true);

CREATE POLICY "Users can view their own progress"
  ON user_progress FOR SELECT
  USING (auth.uid() = user_id);
```

### Step 3: Authentication Migration

```typescript
// Supabase auth setup
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = "YOUR_SUPABASE_URL";
const supabaseKey = "YOUR_SUPABASE_ANON_KEY";

export const supabase = createClient(supabaseUrl, supabaseKey);

// Auth hook
export function useAuth() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  return { user, loading };
}
```

## 📋 **Phase 2: Core Features (Week 3-4)**

### Step 1: Course Management

```typescript
// Course service
export class CourseService {
  static async getCourses() {
    const { data, error } = await supabase
      .from("courses")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data;
  }

  static async getCourse(id: string) {
    const { data, error } = await supabase
      .from("courses")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;
    return data;
  }

  static async createCourse(course: CreateCourseInput) {
    const { data, error } = await supabase
      .from("courses")
      .insert([course])
      .select()
      .single();

    if (error) throw error;
    return data;
  }
}
```

### Step 2: Real-time Progress Tracking

```typescript
// Progress tracking component
export function ProgressTracker({ userId, courseId }: Props) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    // Subscribe to real-time changes
    const subscription = supabase
      .channel(`progress-${userId}-${courseId}`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "user_progress",
          filter: `user_id=eq.${userId}&course_id=eq.${courseId}`,
        },
        (payload) => {
          if (payload.new) {
            setProgress(payload.new.progress_percentage);
          }
        }
      )
      .subscribe();

    return () => subscription.unsubscribe();
  }, [userId, courseId]);

  const updateProgress = async (newProgress: number) => {
    const { error } = await supabase.from("user_progress").upsert({
      user_id: userId,
      course_id: courseId,
      progress_percentage: newProgress,
    });

    if (error) console.error("Error updating progress:", error);
  };

  return (
    <div className="w-full bg-gray-200 rounded-full h-2.5">
      <div
        className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
}
```

## 📋 **Phase 3: Advanced Features (Week 5-6)**

### Step 1: Video Streaming

```typescript
// Video player with progress tracking
export function VideoPlayer({ videoUrl, courseId, onProgress }: Props) {
  const { user } = useAuth();

  const handleProgress = useCallback(
    (progress: number) => {
      if (user) {
        // Update progress in real-time
        supabase.from("user_progress").upsert({
          user_id: user.id,
          course_id: courseId,
          progress_percentage: progress,
        });
      }
      onProgress?.(progress);
    },
    [user, courseId, onProgress]
  );

  return (
    <ReactPlayer
      url={videoUrl}
      controls
      onProgress={({ played }) => handleProgress(played * 100)}
      config={{
        file: {
          attributes: {
            preload: "metadata",
          },
        },
      }}
    />
  );
}
```

### Step 2: BMad Integration với Edge Functions

```typescript
// Supabase Edge Function: bmad-processor.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

serve(async (req) => {
  const { taskType, data } = await req.json();

  // Process BMad task
  const result = await processBmadTask(taskType, data);

  // Update database
  const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
  );

  await supabase.from("bmad_workflows").insert({
    task_type: taskType,
    result: result,
    status: "completed",
  });

  return new Response(JSON.stringify(result), {
    headers: { "Content-Type": "application/json" },
  });
});
```

## 📋 **Phase 4: Deployment & Optimization (Week 7-8)**

### Step 1: Build Optimization

```typescript
// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["react", "react-dom"],
          supabase: ["@supabase/supabase-js"],
          ui: ["@headlessui/react", "tailwindcss"],
        },
      },
    },
  },
  server: {
    port: 3000,
  },
});
```

### Step 2: Deployment Setup

```yaml
# netlify.toml
[build]
  command = "npm run build"
  publish = "dist"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  VITE_SUPABASE_URL = "YOUR_SUPABASE_URL"
  VITE_SUPABASE_ANON_KEY = "YOUR_SUPABASE_ANON_KEY"
```

## 🔄 **Data Migration Strategy**

### Database Migration

```sql
-- Export from current PostgreSQL
pg_dump -h current-host -U username -d database_name > backup.sql

-- Import to Supabase
psql -h db.xxx.supabase.co -U postgres -d postgres < backup.sql
```

### File Migration

```typescript
// Migrate files to Supabase Storage
async function migrateFiles() {
  const files = await getCurrentFiles(); // From AWS S3

  for (const file of files) {
    const { error } = await supabase.storage
      .from("course-materials")
      .upload(file.path, file.buffer);

    if (error) console.error("Migration error:", error);
  }
}
```

## 📊 **Performance Improvements Expected**

| Metric      | Next.js        | React + Supabase | Improvement          |
| ----------- | -------------- | ---------------- | -------------------- |
| Build Time  | 3-5s           | 500ms            | 83% faster           |
| Bundle Size | 250KB          | 120KB            | 52% smaller          |
| First Load  | 1-2s           | 600ms            | 40% faster           |
| Development | Medium         | Fast             | Significantly better |
| Cost        | $115-850/month | $0-45/month      | Up to 95% cheaper    |

## ⚠️ **Migration Risks & Mitigation**

### Risk 1: SEO Impact

- **Risk:** SPA có thể ảnh hưởng SEO
- **Mitigation:** Dùng React Helmet, pre-render static pages

### Risk 2: Real-time Performance

- **Risk:** Supabase realtime có thể chậm hơn Redis
- **Mitigation:** Test performance, có thể dùng WebSocket fallback

### Risk 3: Data Loss

- **Risk:** Migration có thể mất data
- **Mitigation:** Full backup, gradual migration, rollback plan

## 🎯 **Success Metrics**

- [ ] 50% reduction in build time
- [ ] 80% reduction in monthly costs
- [ ] Improved developer experience
- [ ] Maintained or improved user experience
- [ ] Successful BMad integration
- [ ] Real-time features working properly

## 📋 **Go-Live Checklist**

- [ ] All data migrated successfully
- [ ] Authentication working
- [ ] Real-time features tested
- [ ] Video streaming optimized
- [ ] BMad integration functional
- [ ] Performance benchmarks met
- [ ] Monitoring setup complete
- [ ] Rollback plan ready
